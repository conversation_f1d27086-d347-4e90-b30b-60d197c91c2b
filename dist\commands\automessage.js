"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const WelcomeTemplate_1 = require("../models/WelcomeTemplate");
const automessageService_1 = require("../services/automessageService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('automessage')
        .setDescription('Manage automated messages for various server events')
        .addStringOption(option => option.setName('action')
        .setDescription('Action to perform')
        .setRequired(true)
        .addChoices({ name: 'Create Message', value: 'create' }, { name: 'Remove Message', value: 'remove' }, { name: 'List Messages', value: 'list' }, { name: 'Test Message', value: 'test' }))
        .addStringOption(option => option.setName('trigger')
        .setDescription('Event that triggers the message')
        .setRequired(false)
        .addChoices({ name: 'Member Join', value: 'member_join' }, { name: 'Role Added', value: 'role_add' }, { name: 'Role Removed', value: 'role_remove' }))
        .addStringOption(option => option.setName('delivery')
        .setDescription('How to deliver the message')
        .setRequired(false)
        .addChoices({ name: 'Direct Message', value: 'dm' }, { name: 'Channel', value: 'channel' }, { name: 'Both DM and Channel', value: 'both' }))
        .addStringOption(option => option.setName('name')
        .setDescription('Name for the message template')
        .setRequired(false))
        .addRoleOption(option => option.setName('role')
        .setDescription('Specific role for role_add/role_remove triggers')
        .setRequired(false))
        .addChannelOption(option => option.setName('channel')
        .setDescription('Channel to send messages to (for channel/both delivery)')
        .setRequired(false)
        .addChannelTypes(discord_js_1.ChannelType.GuildText))
        .addStringOption(option => option.setName('title')
        .setDescription('Title for the embed message (max 256 characters)')
        .setRequired(false))
        .addStringOption(option => option.setName('description')
        .setDescription('Main content of the message (max 4000 characters)')
        .setRequired(false))
        .addStringOption(option => option.setName('image')
        .setDescription('URL to an image to display in the embed')
        .setRequired(false))
        .addStringOption(option => option.setName('color')
        .setDescription('Hex color code for the embed (e.g., #dd7d00)')
        .setRequired(false))
        .addStringOption(option => option.setName('buttons')
        .setDescription('Buttons in format: Name1|URL1 Name2|URL2 (max 5 buttons)')
        .setRequired(false))
        .addBooleanOption(option => option.setName('embed')
        .setDescription('Send as embed (true) or plain text (false). Default: true')
        .setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const action = interaction.options.getString('action', true);
        try {
            switch (action) {
                case 'create':
                    await handleCreate(interaction);
                    break;
                case 'remove':
                    await handleRemove(interaction);
                    break;
                case 'list':
                    await handleList(interaction);
                    break;
                case 'test':
                    await handleTest(interaction);
                    break;
                default:
                    throw new errorHandler_1.ValidationError('Invalid action specified.');
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError || error instanceof errorHandler_1.DatabaseError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while processing the automessage command.');
        }
    })
};
/**
 * Handles creating a new automated message
 */
async function handleCreate(interaction) {
    const trigger = interaction.options.getString('trigger');
    const delivery = interaction.options.getString('delivery');
    const name = interaction.options.getString('name');
    const role = interaction.options.getRole('role');
    const channel = interaction.options.getChannel('channel');
    const title = interaction.options.getString('title');
    const description = interaction.options.getString('description');
    const image = interaction.options.getString('image');
    const color = interaction.options.getString('color');
    const buttons = interaction.options.getString('buttons');
    const useEmbed = interaction.options.getBoolean('embed') ?? true;
    // Validation
    if (!trigger) {
        throw new errorHandler_1.ValidationError('Trigger type is required for creating messages.');
    }
    if (!delivery) {
        throw new errorHandler_1.ValidationError('Delivery method is required for creating messages.');
    }
    if (!name) {
        throw new errorHandler_1.ValidationError('Message name is required for creating messages.');
    }
    // Validate name length and uniqueness
    if (name.length > 100) {
        throw new errorHandler_1.ValidationError('Message name cannot exceed 100 characters.');
    }
    const existingTemplate = await WelcomeTemplate_1.WelcomeTemplate.findOne({
        guildId: interaction.guild.id,
        name: name
    });
    if (existingTemplate) {
        throw new errorHandler_1.ValidationError(`A message template with the name "${name}" already exists.`);
    }
    // Validate role requirement for role triggers
    if ((trigger === 'role_add' || trigger === 'role_remove') && !role) {
        throw new errorHandler_1.ValidationError('A specific role must be selected for role_add and role_remove triggers.');
    }
    // Validate channel requirement for channel delivery
    if ((delivery === 'channel' || delivery === 'both') && !channel) {
        throw new errorHandler_1.ValidationError('A channel must be selected for channel or both delivery methods.');
    }
    // Validate channel type
    if (channel && channel.type !== discord_js_1.ChannelType.GuildText) {
        throw new errorHandler_1.ValidationError('The specified channel must be a text channel.');
    }
    // Validate content based on format
    if (useEmbed) {
        // For embeds, require either title or description
        if (!title && !description) {
            throw new errorHandler_1.ValidationError('Either title or description must be provided for embed messages.');
        }
    }
    else {
        // For plain text, require description (title is ignored)
        if (!description) {
            throw new errorHandler_1.ValidationError('Description is required for plain text messages.');
        }
        // Warn about ignored embed-specific fields for plain text
        if (title || color || image) {
            // Note: We'll still save these but they won't be used in plain text mode
        }
    }
    if (title && title.length > 256) {
        throw new errorHandler_1.ValidationError('Title cannot exceed 256 characters.');
    }
    if (description && description.length > 4000) {
        throw new errorHandler_1.ValidationError('Description cannot exceed 4000 characters.');
    }
    // Validate color format
    if (color && !/^#[0-9A-Fa-f]{6}$/.test(color)) {
        throw new errorHandler_1.ValidationError('Color must be a valid hex code (e.g., #dd7d00).');
    }
    // Validate image URL
    if (image && !isValidUrl(image)) {
        throw new errorHandler_1.ValidationError('Image must be a valid URL.');
    }
    // Parse and validate buttons
    const parsedButtons = parseButtons(buttons);
    // Map trigger types
    const triggerTypeMap = {
        'member_join': 'join',
        'role_add': 'role_add',
        'role_remove': 'role_remove'
    };
    // Map delivery types
    const deliveryTypeMap = {
        'dm': 'dm',
        'channel': 'channel',
        'both': 'both'
    };
    // Create the template
    const template = new WelcomeTemplate_1.WelcomeTemplate({
        guildId: interaction.guild.id,
        name: name,
        triggerType: triggerTypeMap[trigger],
        triggerRoleId: role?.id,
        deliveryType: deliveryTypeMap[delivery],
        channelId: channel?.id,
        useEmbed: useEmbed,
        title: title || undefined,
        description: description || undefined,
        color: color || undefined,
        imageUrl: image || undefined,
        buttons: parsedButtons,
        enabled: true,
        showTimestamp: true,
        delaySeconds: 0,
        priority: 1,
        fields: []
    });
    await template.save();
    // Create success response
    const embed = (0, embedBuilder_1.createSuccessEmbed)('Automated Message Created')
        .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Message Template Ready!**\n\nYour automated message "${name}" has been created successfully.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Configuration`,
        value: `**Name:** ${name}\n` +
            `**Trigger:** ${getTriggerDisplayName(trigger, role)}\n` +
            `**Delivery:** ${getDeliveryDisplayName(delivery, channel)}\n` +
            `**Format:** ${useEmbed ? 'Embed' : 'Plain Text'}\n` +
            `**Status:** Enabled`,
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Content Preview`,
        value: `**Title:** ${title || 'None'}\n` +
            `**Description:** ${description ? (description.length > 100 ? description.substring(0, 100) + '...' : description) : 'None'}\n` +
            `**Buttons:** ${parsedButtons.length} button(s)`,
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Next Steps`,
        value: `• Use \`/automessage action:test name:${name}\` to preview the message\n` +
            `• Use \`/automessage action:list\` to see all your messages\n` +
            `• Use \`/automessage action:remove name:${name}\` to delete this message`,
        inline: false
    });
    await interaction.reply({ embeds: [embed], ephemeral: false });
}
/**
 * Handles removing an automated message
 */
async function handleRemove(interaction) {
    const name = interaction.options.getString('name');
    if (!name) {
        throw new errorHandler_1.ValidationError('Message name is required for removing messages.');
    }
    const template = await WelcomeTemplate_1.WelcomeTemplate.findOne({
        guildId: interaction.guild.id,
        name: name
    });
    if (!template) {
        throw new errorHandler_1.ValidationError(`No message template found with the name "${name}".`);
    }
    await WelcomeTemplate_1.WelcomeTemplate.findByIdAndDelete(template._id);
    const embed = (0, embedBuilder_1.createSuccessEmbed)('Automated Message Removed')
        .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Message Deleted!**\n\nThe automated message "${name}" has been removed successfully.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} What was removed`,
        value: `**Name:** ${template.name}\n` +
            `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType, null)}\n` +
            `**Delivery:** ${template.deliveryType}`,
        inline: false
    });
    await interaction.reply({ embeds: [embed], ephemeral: false });
}
/**
 * Handles listing all automated messages
 */
async function handleList(interaction) {
    const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
        guildId: interaction.guild.id
    }).sort({ name: 1 });
    if (templates.length === 0) {
        const embed = (0, embedBuilder_1.createAdminEmbed)('No Automated Messages')
            .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.INFO} **No Messages Found**\n\nYou haven't created any automated messages yet.`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Getting Started`,
            value: `Use \`/automessage action:create\` to create your first automated message.\n\n` +
                `**Example:**\n` +
                `\`/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!\``,
            inline: false
        });
        await interaction.reply({ embeds: [embed], ephemeral: false });
        return;
    }
    const embed = (0, embedBuilder_1.createAdminEmbed)('Automated Messages')
        .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Server Message Templates**\n\nFound ${templates.length} automated message(s) configured for this server.`);
    // Group templates by trigger type
    const groupedTemplates = {};
    templates.forEach(template => {
        const triggerKey = template.triggerType === 'join' ? 'member_join' : template.triggerType;
        if (!groupedTemplates[triggerKey]) {
            groupedTemplates[triggerKey] = [];
        }
        groupedTemplates[triggerKey].push(template);
    });
    // Add fields for each trigger type
    Object.entries(groupedTemplates).forEach(([triggerType, templateList]) => {
        const triggerEmoji = getTriggerEmoji(triggerType);
        const triggerName = getTriggerDisplayName(triggerType, null);
        const templateInfo = templateList.map(template => {
            const status = template.enabled ? '🟢' : '🔴';
            const delivery = template.deliveryType;
            const roleInfo = template.triggerRoleId ? ` (${template.triggerRoleId})` : '';
            return `${status} **${template.name}** - ${delivery}${roleInfo}`;
        }).join('\n');
        embed.addFields({
            name: `${triggerEmoji} ${triggerName}`,
            value: templateInfo,
            inline: false
        });
    });
    embed.addFields({
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Management Commands`,
        value: `• \`/automessage action:test name:<name>\` - Preview a message\n` +
            `• \`/automessage action:remove name:<name>\` - Delete a message\n` +
            `• \`/placeholders\` - View available placeholders`,
        inline: false
    });
    await interaction.reply({ embeds: [embed], ephemeral: false });
}
/**
 * Handles testing an automated message
 */
async function handleTest(interaction) {
    const name = interaction.options.getString('name');
    if (!name) {
        throw new errorHandler_1.ValidationError('Message name is required for testing messages.');
    }
    const template = await WelcomeTemplate_1.WelcomeTemplate.findOne({
        guildId: interaction.guild.id,
        name: name
    });
    if (!template) {
        throw new errorHandler_1.ValidationError(`No message template found with the name "${name}".`);
    }
    try {
        const testResult = await (0, automessageService_1.processTestMessage)(interaction.member, template);
        const embed = (0, embedBuilder_1.createSuccessEmbed)('Message Test Preview')
            .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Test Successful!**\n\nHere's how your "${name}" message will look:`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Template Info`,
            value: `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType, null)}\n` +
                `**Delivery:** ${template.deliveryType}\n` +
                `**Format:** ${template.useEmbed ? 'Embed' : 'Plain Text'}\n` +
                `**Status:** ${template.enabled ? 'Enabled' : 'Disabled'}`,
            inline: false
        });
        await interaction.reply({ embeds: [embed], ephemeral: true });
        // Send the actual test message
        if (testResult.embed) {
            await interaction.followUp({
                embeds: [testResult.embed],
                components: testResult.components || [],
                ephemeral: true
            });
        }
        else if (testResult.content) {
            await interaction.followUp({
                content: testResult.content,
                components: testResult.components || [],
                ephemeral: true
            });
        }
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to test message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Utility function to validate URLs
 */
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    }
    catch (_) {
        return false;
    }
}
/**
 * Utility function to parse button strings
 */
function parseButtons(buttonString) {
    if (!buttonString)
        return [];
    const buttons = [];
    const buttonPairs = buttonString.split(' ');
    for (const pair of buttonPairs) {
        if (buttons.length >= 5) {
            throw new errorHandler_1.ValidationError('Maximum of 5 buttons allowed.');
        }
        const [name, url] = pair.split('|');
        if (!name || !url) {
            throw new errorHandler_1.ValidationError('Button format must be: Name|URL (e.g., Discord|https://discord.com)');
        }
        if (name.length > 80) {
            throw new errorHandler_1.ValidationError('Button names cannot exceed 80 characters.');
        }
        if (!isValidUrl(url)) {
            throw new errorHandler_1.ValidationError(`Invalid URL for button "${name}": ${url}`);
        }
        buttons.push({
            label: name,
            url: url,
            style: 'Link'
        });
    }
    return buttons;
}
/**
 * Utility function to get display name for triggers
 */
function getTriggerDisplayName(trigger, role) {
    switch (trigger) {
        case 'member_join':
            return 'Member Join';
        case 'role_add':
            return `Role Added${role ? ` (${role.name})` : ''}`;
        case 'role_remove':
            return `Role Removed${role ? ` (${role.name})` : ''}`;
        default:
            return trigger;
    }
}
/**
 * Utility function to get display name for delivery methods
 */
function getDeliveryDisplayName(delivery, channel) {
    switch (delivery) {
        case 'dm':
            return 'Direct Message';
        case 'channel':
            return `Channel${channel ? ` (${channel.name})` : ''}`;
        case 'both':
            return `Both DM and Channel${channel ? ` (${channel.name})` : ''}`;
        default:
            return delivery;
    }
}
/**
 * Utility function to get emoji for trigger types
 */
function getTriggerEmoji(trigger) {
    switch (trigger) {
        case 'member_join':
            return '👋';
        case 'role_add':
            return '🎭';
        case 'role_remove':
            return '🗑️';
        default:
            return '📝';
    }
}
