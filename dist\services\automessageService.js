"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.processJoinMessage = processJoinMessage;
exports.processRoleChangeMessage = processRoleChangeMessage;
exports.processTestMessage = processTestMessage;
const discord_js_1 = require("discord.js");
const WelcomeTemplate_1 = require("../models/WelcomeTemplate");
/**
 * Processes automated messages for member join events
 */
async function processJoinMessage(member) {
    const result = {
        sent: false,
        templatesProcessed: 0,
        errors: []
    };
    try {
        // Find all enabled join templates for this guild
        const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
            guildId: member.guild.id,
            triggerType: 'join',
            enabled: true
        }).sort({ priority: -1 });
        if (templates.length === 0) {
            return result; // No templates configured
        }
        for (const template of templates) {
            try {
                await processTemplate(member, template, null);
                result.templatesProcessed++;
                result.sent = true;
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Template "${template.name}": ${errorMessage}`);
                console.error(`[AutoMessage] Error processing join template "${template.name}":`, error);
            }
        }
        return result;
    }
    catch (error) {
        result.errors.push(`Failed to fetch join templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return result;
    }
}
/**
 * Processes automated messages for role change events
 */
async function processRoleChangeMessage(member, role, triggerType) {
    const result = {
        sent: false,
        templatesProcessed: 0,
        errors: []
    };
    try {
        // Find all enabled role change templates for this guild and role
        const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
            guildId: member.guild.id,
            triggerType: triggerType,
            triggerRoleId: role.id,
            enabled: true
        }).sort({ priority: -1 });
        if (templates.length === 0) {
            return result; // No templates configured for this role
        }
        for (const template of templates) {
            try {
                await processTemplate(member, template, role);
                result.templatesProcessed++;
                result.sent = true;
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Template "${template.name}": ${errorMessage}`);
                console.error(`[AutoMessage] Error processing role change template "${template.name}":`, error);
            }
        }
        return result;
    }
    catch (error) {
        result.errors.push(`Failed to fetch role change templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return result;
    }
}
/**
 * Creates a test message for preview
 */
async function processTestMessage(member, template) {
    try {
        const embed = await createMessageEmbed(member, template, null);
        const components = createMessageComponents(template);
        return {
            embed,
            components: components.length > 0 ? components : undefined
        };
    }
    catch (error) {
        throw new Error(`Failed to create test message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Processes a single template and sends the message
 */
async function processTemplate(member, template, role) {
    // Apply delay if specified
    if (template.delaySeconds > 0) {
        await new Promise(resolve => setTimeout(resolve, template.delaySeconds * 1000));
    }
    // Create the embed and components
    const embed = await createMessageEmbed(member, template, role);
    const components = createMessageComponents(template);
    const messageOptions = {
        embeds: [embed],
        components: components.length > 0 ? components : undefined
    };
    // Send based on delivery type
    switch (template.deliveryType) {
        case 'dm':
            try {
                await member.send(messageOptions);
            }
            catch (error) {
                throw new Error(`Failed to send DM: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            break;
        case 'channel':
            if (!template.channelId) {
                throw new Error('No channel specified for channel delivery');
            }
            const channel = member.guild.channels.cache.get(template.channelId);
            if (!channel || !channel.isTextBased()) {
                throw new Error('Channel not found or not text-based');
            }
            try {
                await channel.send(messageOptions);
            }
            catch (error) {
                throw new Error(`Failed to send to channel: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            break;
        case 'both':
            if (!template.channelId) {
                throw new Error('No channel specified for both delivery');
            }
            const bothChannel = member.guild.channels.cache.get(template.channelId);
            if (!bothChannel || !bothChannel.isTextBased()) {
                throw new Error('Channel not found or not text-based');
            }
            // Send to both DM and channel
            const errors = [];
            try {
                await member.send(messageOptions);
            }
            catch (error) {
                errors.push(`DM failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            try {
                await bothChannel.send(messageOptions);
            }
            catch (error) {
                errors.push(`Channel failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            if (errors.length === 2) {
                throw new Error(`Both delivery methods failed: ${errors.join(', ')}`);
            }
            break;
        default:
            throw new Error(`Unknown delivery type: ${template.deliveryType}`);
    }
}
/**
 * Creates an embed from a template with placeholder replacement
 */
async function createMessageEmbed(member, template, role) {
    // Start with base embed
    const embed = new discord_js_1.EmbedBuilder()
        .setColor((template.color || '#dd7d00'));
    // Set title with placeholder replacement
    if (template.title) {
        embed.setTitle(replacePlaceholders(template.title, member, role));
    }
    // Set description with placeholder replacement
    if (template.description) {
        embed.setDescription(replacePlaceholders(template.description, member, role));
    }
    // Set image
    if (template.imageUrl) {
        embed.setImage(template.imageUrl);
    }
    // Set thumbnail
    if (template.thumbnailUrl) {
        embed.setThumbnail(template.thumbnailUrl);
    }
    else {
        // Default to user avatar for join messages
        if (template.triggerType === 'join') {
            embed.setThumbnail(member.displayAvatarURL({ size: 128 }));
        }
    }
    // Set footer
    if (template.footerText) {
        embed.setFooter({ text: replacePlaceholders(template.footerText, member, role) });
    }
    // Set timestamp
    if (template.showTimestamp) {
        embed.setTimestamp();
    }
    // Add custom fields
    if (template.fields && template.fields.length > 0) {
        for (const field of template.fields) {
            embed.addFields({
                name: replacePlaceholders(field.name, member, role),
                value: replacePlaceholders(field.value, member, role),
                inline: field.inline || false
            });
        }
    }
    return embed;
}
/**
 * Creates action row components from template buttons
 */
function createMessageComponents(template) {
    if (!template.buttons || template.buttons.length === 0) {
        return [];
    }
    const buttons = template.buttons.map(button => new discord_js_1.ButtonBuilder()
        .setLabel(button.label)
        .setURL(button.url)
        .setStyle(discord_js_1.ButtonStyle.Link));
    // Discord allows max 5 buttons per action row
    const actionRows = [];
    for (let i = 0; i < buttons.length; i += 5) {
        const row = new discord_js_1.ActionRowBuilder()
            .addComponents(buttons.slice(i, i + 5));
        actionRows.push(row);
    }
    return actionRows;
}
/**
 * Replaces placeholders in text with actual values
 */
function replacePlaceholders(text, member, role) {
    // First process line breaks for multi-line support
    let result = text
        .replace(/\\n/g, '\n')
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n');
    // User placeholders
    result = result.replace(/{user}/g, member.displayName);
    result = result.replace(/{user\.mention}/g, `<@${member.id}>`);
    result = result.replace(/{user\.username}/g, member.user.username);
    result = result.replace(/{user\.displayName}/g, member.displayName);
    result = result.replace(/{user\.id}/g, member.id);
    result = result.replace(/{user\.tag}/g, member.user.tag);
    // Server placeholders
    result = result.replace(/{server}/g, member.guild.name);
    result = result.replace(/{server\.name}/g, member.guild.name);
    result = result.replace(/{server\.id}/g, member.guild.id);
    result = result.replace(/{server\.memberCount}/g, member.guild.memberCount.toString());
    // Role placeholders (if role is provided)
    if (role) {
        result = result.replace(/{role}/g, role.name);
        result = result.replace(/{role\.name}/g, role.name);
        result = result.replace(/{role\.id}/g, role.id);
        result = result.replace(/{role\.mention}/g, `<@&${role.id}>`);
    }
    // Date/time placeholders
    const now = new Date();
    result = result.replace(/{date}/g, now.toLocaleDateString());
    result = result.replace(/{time}/g, now.toLocaleTimeString());
    result = result.replace(/{datetime}/g, now.toLocaleString());
    return result;
}
